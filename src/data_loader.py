"""Data loading and validation for DS-160 form data."""
import json
import re
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from pydantic import BaseModel, Field, validator, model_validator, EmailStr
from enum import Enum

# DS-160 Street Address Validation Pattern
# Only allows: A-Z, 0-9, #, $, *, %, &, (;), !, @, ^, ?, >, <, parens (), period (.), apostrophe ('), comma (,), hyphen (-), and space
DS160_STREET_ADDRESS_PATTERN = r"^[A-Z0-9#$*%&(;)!@^?><().',\-\s]+$"

def validate_ds160_street_address(address: str) -> str:
    """Validate street address according to DS-160 requirements."""
    if not address:
        raise ValueError("Street address cannot be empty")
    
    # Convert to uppercase as DS-160 expects
    address = address.upper().strip()
    
    # Check against DS-160 allowed characters pattern
    if not re.match(DS160_STREET_ADDRESS_PATTERN, address):
        invalid_chars = set(address) - set("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789#$*%&(;)!@^?><().',- ")
        raise ValueError(
            f"Street address contains invalid characters: {', '.join(sorted(invalid_chars))}. "
            f"Only the following characters are valid: A-Z, 0-9, #, $, *, %, &, (;), !, @, ^, ?, >, <, parens (), period (.), apostrophe ('), comma (,), hyphen (-), and space."
        )
    
    return address

class Gender(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class MaritalStatus(str, Enum):
    """Семейное положение с правильными кодами из DS-160 HTML."""
    SINGLE = "S"
    MARRIED = "M"
    DIVORCED = "D"
    WIDOWED = "W"
    LEGALLY_SEPARATED = "L"
    COMMON_LAW = "C"
    CIVIL_UNION = "P"
    OTHER = "O"

    @classmethod
    def _missing_(cls, value):
        """Автоматическое преобразование старых значений в новые коды."""
        # Mapping старых полных названий к новым кодам
        legacy_mapping = {
            "SINGLE": "S",
            "MARRIED": "M", 
            "DIVORCED": "D",
            "WIDOWED": "W",
            "LEGALLY_SEPARATED": "L",
            "COMMON_LAW": "C",
            "CIVIL_UNION": "P",
            "DOMESTIC_PARTNERSHIP": "P",  # alias для CIVIL_UNION
            "OTHER": "O"
        }
        
        # Проверяем точное совпадение
        if value in legacy_mapping:
            return cls(legacy_mapping[value])
        
        # Проверяем case-insensitive совпадение
        value_upper = str(value).upper()
        if value_upper in legacy_mapping:
            return cls(legacy_mapping[value_upper])
        
        # Если не найдено, возвращаем None (будет ошибка валидации)
        return None

class AddressType(str, Enum):
    """Address type options for DS-160 forms."""
    HOME = "H"
    MAILING = "M"
    US_CONTACT = "U"
    DO_NOT_KNOW = "D"
    OTHER = "O"

class PrimaryOccupation(str, Enum):
    """Primary occupation categories available in DS-160 dropdown (actual form data)."""
    AGRICULTURE = "A"
    ARTIST_PERFORMER = "AP"
    BUSINESS = "B"
    COMMUNICATIONS = "CM"
    COMPUTER_SCIENCE = "CS"
    CULINARY_FOOD_SERVICES = "C"
    EDUCATION = "ED"
    ENGINEERING = "EN"
    GOVERNMENT = "G"
    HOMEMAKER = "H"
    LEGAL_PROFESSION = "LP"
    MEDICAL_HEALTH = "MH"
    MILITARY = "M"
    NATURAL_SCIENCE = "NS"
    NOT_EMPLOYED = "N"
    PHYSICAL_SCIENCES = "PS"
    RELIGIOUS_VOCATION = "RV"
    RESEARCH = "R"
    RETIRED = "RT"
    SOCIAL_SCIENCE = "SS"
    STUDENT = "S"
    OTHER = "O"
    
    @classmethod
    def get_best_match(cls, occupation: str) -> str:
        """Find the best matching DS-160 occupation category for a given occupation string."""
        occupation_upper = occupation.upper()
        
        # Direct mapping for common occupations to DS-160 categories
        modern_mappings = {
            # Business and Management
            "SOCIAL MEDIA MANAGER": cls.BUSINESS,
            "DIGITAL MARKETING MANAGER": cls.BUSINESS,
            "CONTENT MANAGER": cls.BUSINESS,
            "MARKETING MANAGER": cls.BUSINESS,
            "PROJECT MANAGER": cls.BUSINESS,
            "PRODUCT MANAGER": cls.BUSINESS,
            "BUSINESS ANALYST": cls.BUSINESS,
            "BUSINESS CONSULTANT": cls.BUSINESS,
            "CONSULTANT": cls.BUSINESS,
            "INTERNATIONAL CONSULTANT": cls.BUSINESS,
            "CEO": cls.BUSINESS,
            "PRESIDENT": cls.BUSINESS,
            "ENTREPRENEUR": cls.BUSINESS,
            "EXECUTIVE": cls.BUSINESS,
            "MANAGER": cls.BUSINESS,
            "SALES": cls.BUSINESS,
            "MARKETING": cls.BUSINESS,
            "FINANCE": cls.BUSINESS,
            "ACCOUNTING": cls.BUSINESS,
            "ACCOUNTANT": cls.BUSINESS,
            
            # Technology and Computer Science
            "SOFTWARE ENGINEER": cls.COMPUTER_SCIENCE,
            "SOFTWARE DEVELOPER": cls.COMPUTER_SCIENCE,
            "WEB DEVELOPER": cls.COMPUTER_SCIENCE,
            "DATA SCIENTIST": cls.COMPUTER_SCIENCE,
            "PROGRAMMER": cls.COMPUTER_SCIENCE,
            "IT SPECIALIST": cls.COMPUTER_SCIENCE,
            "COMPUTER PROGRAMMER": cls.COMPUTER_SCIENCE,
            "SYSTEM ADMINISTRATOR": cls.COMPUTER_SCIENCE,
            "DATABASE ADMINISTRATOR": cls.COMPUTER_SCIENCE,
            "CYBERSECURITY": cls.COMPUTER_SCIENCE,
            
            # Engineering
            "ENGINEER": cls.ENGINEERING,
            "CIVIL ENGINEER": cls.ENGINEERING,
            "MECHANICAL ENGINEER": cls.ENGINEERING,
            "ELECTRICAL ENGINEER": cls.ENGINEERING,
            "CHEMICAL ENGINEER": cls.ENGINEERING,
            
            # Arts and Design
            "ARTIST": cls.ARTIST_PERFORMER,
            "DESIGNER": cls.ARTIST_PERFORMER,
            "GRAPHIC DESIGNER": cls.ARTIST_PERFORMER,
            "UX DESIGNER": cls.ARTIST_PERFORMER,
            "PHOTOGRAPHER": cls.ARTIST_PERFORMER,
            "MUSICIAN": cls.ARTIST_PERFORMER,
            "ACTOR": cls.ARTIST_PERFORMER,
            "WRITER": cls.ARTIST_PERFORMER,
            "JOURNALIST": cls.ARTIST_PERFORMER,
            
            # Medical and Health
            "DOCTOR": cls.MEDICAL_HEALTH,
            "PHYSICIAN": cls.MEDICAL_HEALTH,
            "NURSE": cls.MEDICAL_HEALTH,
            "DENTIST": cls.MEDICAL_HEALTH,
            "PHARMACIST": cls.MEDICAL_HEALTH,
            "THERAPIST": cls.MEDICAL_HEALTH,
            "MEDICAL": cls.MEDICAL_HEALTH,
            
            # Education
            "TEACHER": cls.EDUCATION,
            "PROFESSOR": cls.EDUCATION,
            "EDUCATOR": cls.EDUCATION,
            "INSTRUCTOR": cls.EDUCATION,
            "TUTOR": cls.EDUCATION,
            
            # Legal
            "LAWYER": cls.LEGAL_PROFESSION,
            "ATTORNEY": cls.LEGAL_PROFESSION,
            "JUDGE": cls.LEGAL_PROFESSION,
            "LEGAL": cls.LEGAL_PROFESSION,
            
            # Government
            "GOVERNMENT EMPLOYEE": cls.GOVERNMENT,
            "CIVIL SERVANT": cls.GOVERNMENT,
            "DIPLOMAT": cls.GOVERNMENT,
            "POLICE": cls.GOVERNMENT,
            "FIREFIGHTER": cls.GOVERNMENT,
            
            # Communications/Media
            "COMMUNICATIONS": cls.COMMUNICATIONS,
            "MEDIA": cls.COMMUNICATIONS,
            "PR": cls.COMMUNICATIONS,
            "PUBLIC RELATIONS": cls.COMMUNICATIONS,
            "ADVERTISING": cls.COMMUNICATIONS,
            
            # Research and Science
            "RESEARCHER": cls.RESEARCH,
            "SCIENTIST": cls.NATURAL_SCIENCE,
            "BIOLOGIST": cls.NATURAL_SCIENCE,
            "CHEMIST": cls.PHYSICAL_SCIENCES,
            "PHYSICIST": cls.PHYSICAL_SCIENCES,
            "MATHEMATICIAN": cls.PHYSICAL_SCIENCES,
            
            # Other categories
            "CHEF": cls.CULINARY_FOOD_SERVICES,
            "COOK": cls.CULINARY_FOOD_SERVICES,
            "RESTAURANT": cls.CULINARY_FOOD_SERVICES,
            "FOOD": cls.CULINARY_FOOD_SERVICES,
            "FARMER": cls.AGRICULTURE,
            "AGRICULTURE": cls.AGRICULTURE,
            "MILITARY": cls.MILITARY,
            "SOLDIER": cls.MILITARY,
            "PASTOR": cls.RELIGIOUS_VOCATION,
            "PRIEST": cls.RELIGIOUS_VOCATION,
            "MINISTER": cls.RELIGIOUS_VOCATION,
            "SOCIAL WORKER": cls.SOCIAL_SCIENCE,
            "PSYCHOLOGIST": cls.SOCIAL_SCIENCE,
            "SOCIOLOGIST": cls.SOCIAL_SCIENCE,
            
            # Status-based
            "STUDENT": cls.STUDENT,
            "RETIRED": cls.RETIRED,
            "HOMEMAKER": cls.HOMEMAKER,
            "UNEMPLOYED": cls.NOT_EMPLOYED,
            "NOT EMPLOYED": cls.NOT_EMPLOYED,
            "FREELANCER": cls.OTHER,
        }
        
        # Check direct mappings first
        if occupation_upper in modern_mappings:
            return modern_mappings[occupation_upper].value
        
        # Try partial word matches
        for key, value in modern_mappings.items():
            if key in occupation_upper or any(word in occupation_upper for word in key.split()):
                return value.value
        
        # Category-based partial matches
        if any(word in occupation_upper for word in ["BUSINESS", "MANAGER", "MARKETING", "SALES", "FINANCE", "CONSULTANT"]):
            return cls.BUSINESS.value
        elif any(word in occupation_upper for word in ["SOFTWARE", "COMPUTER", "IT", "TECH", "DEVELOPER", "PROGRAMMER", "DATA"]):
            return cls.COMPUTER_SCIENCE.value
        elif any(word in occupation_upper for word in ["ENGINEER", "ENGINEERING"]):
            return cls.ENGINEERING.value
        elif any(word in occupation_upper for word in ["ARTIST", "DESIGN", "CREATIVE", "MEDIA", "PHOTO", "MUSIC"]):
            return cls.ARTIST_PERFORMER.value
        elif any(word in occupation_upper for word in ["DOCTOR", "MEDICAL", "HEALTH", "NURSE", "PHYSICIAN"]):
            return cls.MEDICAL_HEALTH.value
        elif any(word in occupation_upper for word in ["TEACHER", "EDUCATION", "PROFESSOR", "SCHOOL"]):
            return cls.EDUCATION.value
        elif any(word in occupation_upper for word in ["LAWYER", "LEGAL", "ATTORNEY", "LAW"]):
            return cls.LEGAL_PROFESSION.value
        elif any(word in occupation_upper for word in ["GOVERNMENT", "CIVIL", "PUBLIC", "POLICE"]):
            return cls.GOVERNMENT.value
        elif any(word in occupation_upper for word in ["RESEARCH", "RESEARCHER"]):
            return cls.RESEARCH.value
        elif any(word in occupation_upper for word in ["SCIENCE", "SCIENTIST", "BIOLOGY", "CHEMISTRY"]):
            return cls.NATURAL_SCIENCE.value
        elif any(word in occupation_upper for word in ["STUDENT", "STUDYING"]):
            return cls.STUDENT.value
        elif any(word in occupation_upper for word in ["RETIRED", "RETIREMENT"]):
            return cls.RETIRED.value
        elif any(word in occupation_upper for word in ["HOMEMAKER", "HOUSEWIFE", "HOUSEHUSBAND"]):
            return cls.HOMEMAKER.value
        elif any(word in occupation_upper for word in ["UNEMPLOYED", "NOT EMPLOYED", "JOBLESS"]):
            return cls.NOT_EMPLOYED.value
        
        # Default fallback
        return cls.OTHER.value

class VisaPurpose(str, Enum):
    BUSINESS_TOURISM = "TEMP. BUSINESS OR PLEASURE VISITOR (B)"
    STUDY = "STUDENT (F)"
    WORK = "TEMPORARY WORKER (H)"
    TRANSIT = "TRANSIT (C)"
    OTHER = "OTHER"

class RelationshipType(str, Enum):
    """Relationship types for travel companions."""
    PARENT = "PARENT"
    SPOUSE = "SPOUSE"
    CHILD = "CHILD"
    OTHER_RELATIVE = "OTHER RELATIVE"
    FRIEND = "FRIEND"
    BUSINESS_ASSOCIATE = "BUSINESS ASSOCIATE"
    OTHER = "OTHER"

# Application Info
class ApplicationInfo(BaseModel):
    """Application information."""
    form_type: str = Field(default="DS-160", description="Form type")
    application_id: Optional[str] = Field(None, description="Application ID if resuming")
    application_action: str = Field(default="new", description="Action: 'new' or 'retrieve'")
    # Fields for retrieving existing application
    retrieve_surname: Optional[str] = Field(None, description="Surname for retrieving application")
    retrieve_year_of_birth: Optional[int] = Field(None, description="Year of birth for retrieving application")
    retrieve_security_answer: Optional[str] = Field(None, description="Security question answer for retrieving application")

# Personal Info
class PlaceOfBirth(BaseModel):
    """Place of birth information."""
    model_config = {"populate_by_name": True}  # Поддержка alias
    
    city: Optional[str] = Field(None, min_length=1, max_length=100, description="City - optional, checkbox if not provided")
    state_province: Optional[str] = Field(None, alias="stateProvince", min_length=0)
    country_region: str = Field(..., min_length=1, max_length=100, alias="countryRegion")

class OtherNamesUsed(BaseModel):
    """Other names used information."""
    other_surnames: str = Field(..., min_length=1, max_length=100)
    other_given_names: str = Field(..., min_length=1, max_length=100)

class SpouseInfo(BaseModel):
    """Информация о супруге для MARRIED/COMMON_LAW/CIVIL_UNION."""
    model_config = {"populate_by_name": True}  # Поддержка alias
    
    surnames: str = Field(..., min_length=1, max_length=100, description="Фамилии супруга")
    given_names: str = Field(..., min_length=1, max_length=100, description="Имена супруга", alias="givenNames")
    date_of_birth: date = Field(..., description="Дата рождения супруга", alias="dateOfBirth")
    place_of_birth: PlaceOfBirth = Field(..., description="Место рождения супруга", alias="placeOfBirth")
    nationality: str = Field(..., min_length=1, max_length=100, description="Национальность супруга")
    address_type: AddressType = Field(default=AddressType.HOME, description="Тип адреса супруга", alias="addressType")
    address: Optional['Address'] = Field(None, description="Адрес супруга (только если address_type=O)")
    
    @model_validator(mode='after')
    def validate_address_consistency(self):
        """Автоматически устанавливаем address_type=O если есть адрес."""
        if self.address and self.address_type == "H":  # Если есть адрес, но тип по умолчанию
            self.address_type = "O"  # Устанавливаем "Other"
        return self

class DeceasedSpouseInfo(BaseModel):
    """Информация об умершем супруге для WIDOWED."""
    model_config = {"populate_by_name": True}  # Поддержка automatic conversion
    
    # REQUIRED fields - no aliases, let _convert_keys_to_snake_case() handle conversion
    surnames: str = Field(..., min_length=1, max_length=100, description="Фамилии умершего супруга")
    given_names: str = Field(..., min_length=1, max_length=100, description="Имена умершего супруга")
    date_of_birth: date = Field(..., description="Дата рождения умершего супруга")
    nationality: str = Field(..., min_length=1, max_length=100, description="Национальность умершего супруга")
    
    # REQUIRED nested place_of_birth object (matches JSON structure)
    place_of_birth: PlaceOfBirth = Field(..., description="Место рождения умершего супруга")

class FormerSpouseInfo(BaseModel):
    """Информация о бывшем супруге."""
    
    surnames: str = Field(..., min_length=1, max_length=100, description="Фамилии бывшего супруга")
    given_names: str = Field(..., min_length=1, max_length=100, description="Имена бывшего супруга") 
    date_of_birth: date = Field(..., description="Дата рождения бывшего супруга")
    place_of_birth: Optional[PlaceOfBirth] = Field(None, description="Место рождения бывшего супруга")
    nationality: str = Field(..., min_length=1, max_length=100, description="Национальность бывшего супруга")
    address_type: Optional[str] = Field(None, description="Тип адреса (H/M)")
    # Optional marriage details - may be provided later or separately
    date_of_marriage: Optional[date] = Field(None, description="Дата брака")
    date_marriage_ended: Optional[date] = Field(None, description="Дата окончания брака")
    how_marriage_ended: Optional[str] = Field(None, max_length=4000, description="Как закончился брак")
    marriage_termination_country: Optional[str] = Field(None, min_length=1, max_length=100, description="Страна где брак был расторгнут")

class DivorceInfo(BaseModel):
    """Информация о разводе для DIVORCED."""
    
    number_of_former_spouses: int = Field(..., ge=1, le=10, description="Количество бывших супругов")
    former_spouses: List[FormerSpouseInfo] = Field(..., min_items=1, description="Список бывших супругов")

class PersonalInfo(BaseModel):
    """Personal information section."""
    model_config = {"populate_by_name": True}  # Поддержка alias
    
    surnames: str = Field(..., min_length=1, max_length=100, description="Family name/surname")
    given_names: str = Field(..., min_length=1, max_length=100, description="Given names", alias="givenNames")
    full_name_native_alphabet: Optional[str] = Field(None, description="Full name in native alphabet", alias="fullNameNativeAlphabet")
    other_names_used: List[OtherNamesUsed] = Field(default_factory=list, description="Other names used", alias="otherNamesUsed")
    telecode_represents_name: str = Field(default="No", description="Does telecode represent name", alias="telecodeRepresentsName")
    sex: Gender
    marital_status: MaritalStatus = Field(..., alias="maritalStatus")
    marital_status_other_explanation: Optional[str] = Field(None, max_length=4000, description="Объяснение семейного статуса (только для OTHER)", alias="maritalStatusOtherExplanation")
    date_of_birth: date = Field(..., description="Date of birth (YYYY-MM-DD)", alias="dateOfBirth")
    place_of_birth: Optional[PlaceOfBirth] = Field(None, alias="placeOfBirth")
    spouse: Optional[SpouseInfo] = Field(None, description="Информация о супруге (для MARRIED/COMMON_LAW/CIVIL_UNION)")
    deceased_spouse: Optional[DeceasedSpouseInfo] = Field(None, description="Информация об умершем супруге (для WIDOWED)", alias="deceasedSpouse")
    divorce_info: Optional[DivorceInfo] = Field(None, description="Информация о разводе (для DIVORCED)", alias="divorceInfo")

# Nationality and Residence
class OtherNationality(BaseModel):
    """Other nationality information."""
    country: str = Field(..., min_length=1, max_length=100)
    has_passport: str = Field(default="No", description="Has passport for this nationality")
    passport_number: Optional[str] = Field(None, description="Passport number if has passport")

class NationalityAndResidence(BaseModel):
    """Nationality and residence information."""
    country_of_origin: str = Field(..., min_length=1, max_length=100)
    other_nationalities: List[OtherNationality] = Field(default_factory=list)
    is_permanent_resident_of_other_country: str = Field(default="No")
    permanent_resident_countries: List[str] = Field(default_factory=list)
    national_identification_number: Optional[str] = Field(None, max_length=50)
    us_social_security_number: Optional[str] = Field(None, max_length=15)
    us_taxpayer_id_number: Optional[str] = Field(None, max_length=15)

# Passport Info
class LostPassportInfo(BaseModel):
    """Lost passport information."""
    answer: str = Field(..., description="Yes/No answer")
    lost_passport_number: Optional[str] = Field(None, description="Lost passport number")
    lost_passport_issuing_country: Optional[str] = Field(None, description="Issuing country")
    explanation: Optional[str] = Field(None, description="Explanation")

class PassportInfo(BaseModel):
    """Passport information."""
    passport_type: str = Field(default="REGULAR", description="Type of passport")
    passport_number: str = Field(..., min_length=1, max_length=50)
    passport_book_number: Optional[str] = Field(None, max_length=50)
    issuing_country: str = Field(..., min_length=1, max_length=100)
    issuing_city: str = Field(..., min_length=1, max_length=100)
    issuing_state_province: Optional[str] = Field(None, max_length=100, description="State/province (if applicable)")
    issuance_date: date = Field(..., description="Passport issuance date")
    expiration_date: date = Field(..., description="Passport expiration date")
    has_lost_or_stolen_passport: Optional[LostPassportInfo] = None

# Contact Info
class Address(BaseModel):
    """Address information."""
    street_line1: str = Field(..., min_length=1, max_length=200)
    street_line2: Optional[str] = Field(None, max_length=200)
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None, max_length=100)
    postal_zone_zip_code: Optional[str] = Field(None, max_length=20, description="Postal/ZIP code (if applicable)")
    country_region: str = Field(..., min_length=1, max_length=100)
    
    @validator('street_line1', pre=True)
    def validate_street_line1(cls, v):
        """Validate street address line 1 according to DS-160 requirements."""
        return validate_ds160_street_address(v)
    
    @validator('street_line2', pre=True)
    def validate_street_line2(cls, v):
        """Validate street address line 2 according to DS-160 requirements."""
        if v is not None and v.strip():
            return validate_ds160_street_address(v)
        return v

class USAddress(BaseModel):
    """US address information."""
    street_line1: str = Field(..., min_length=1, max_length=200)
    city: str = Field(..., min_length=1, max_length=100)
    state: str = Field(..., min_length=1, max_length=100)
    zip_code: str = Field(..., min_length=1, max_length=20)
    
    @validator('street_line1', pre=True)
    def validate_street_line1(cls, v):
        """Validate street address line 1 according to DS-160 requirements."""
        return validate_ds160_street_address(v)

class PhoneNumbers(BaseModel):
    """Phone numbers information."""
    primary: str = Field(..., description="Primary phone number")
    secondary: Optional[str] = Field(None, description="Secondary phone number")
    work: Optional[str] = Field(None, description="Work phone number")
    other: Optional[str] = Field(None, description="Other phone number")

class EmailAddresses(BaseModel):
    """Email addresses information."""
    primary: EmailStr = Field(..., description="Primary email address")
    additional: Optional[EmailStr] = Field(None, description="Additional email address")

class SocialMedia(BaseModel):
    """Social media information."""
    platform: str = Field(..., description="Social media platform")
    identifier: str = Field(..., description="Username or identifier")

class ContactInfo(BaseModel):
    """Contact information."""
    home_address: Address = Field(..., description="Home/primary address (required)")
    mailing_address: Optional[Address] = Field(None, description="Mailing address (optional)")
    phone_numbers: PhoneNumbers
    email_addresses: EmailAddresses
    social_media: List[SocialMedia] = Field(default_factory=list)

# Travel Info
class TravelCompanion(BaseModel):
    """Travel companion information."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    relationship: RelationshipType = Field(..., description="Relationship to applicant")

class TravelCompanions(BaseModel):
    """Travel companions information."""
    are_other_persons_traveling: str = Field(default="No")
    persons_traveling: List[TravelCompanion] = Field(default_factory=list)
    is_traveling_as_part_of_group: str = Field(default="No")
    group_name: Optional[str] = Field(None, description="Group name if applicable")

class TravelInfo(BaseModel):
    """Travel information."""
    purpose_of_trip: str = Field(..., description="Purpose of trip")
    visa_class: str = Field(..., description="Visa class")
    has_specific_travel_plans: str = Field(default="Yes")
    arrival_date: date = Field(..., description="Intended arrival date in US")
    arrival_flight: Optional[str] = Field(None, description="Arrival flight number")
    arrival_city: str = Field(..., description="Arrival city")
    departure_date: Optional[date] = Field(None, description="Departure date")
    departure_flight: Optional[str] = Field(None, description="Departure flight number")
    departure_city: Optional[str] = Field(None, description="Departure city")
    locations_to_visit_in_us: List[str] = Field(default=[])
    us_stay_address: USAddress = Field(..., description="Address where you will stay in the US")
    person_entity_paying: str = Field(..., description="Who is paying for the trip")

# Previous US Travel
class PreviousVisit(BaseModel):
    """Previous US visit information."""
    date_arrived: date = Field(..., description="Date of arrival")
    length_of_stay: str = Field(..., description="Length of stay")
    unit_of_stay: str = Field(..., description="Unit of stay (days, months, etc.)")

class DriversLicense(BaseModel):
    """US drivers license information."""
    license_number: str = Field(..., description="License number")
    issuing_state: str = Field(..., description="Issuing state")

class VisaLostOrStolen(BaseModel):
    """Visa lost or stolen information."""
    answer: str = Field(..., description="Yes/No answer")
    year_lost: Optional[str] = Field(None, description="Year lost or stolen")
    explanation: Optional[str] = Field(None, description="Explanation")

class VisaCancelledOrRevoked(BaseModel):
    """Visa cancelled or revoked information."""
    answer: str = Field(..., description="Yes/No answer")
    explanation: Optional[str] = Field(None, description="Explanation")

class PreviousVisaInfo(BaseModel):
    """Previous US visa information."""
    date_last_visa_issued: Optional[date] = Field(None, description="Date last visa was issued")
    annotation: Optional[str] = Field(None, description="Annotation")
    visa_number: Optional[str] = Field(None, description="Visa number")
    is_applying_for_same_type: str = Field(default="Yes")
    is_applying_in_same_country: str = Field(default="Yes")
    has_been_ten_printed: str = Field(default="No")
    has_visa_been_lost_or_stolen: Optional[VisaLostOrStolen] = None
    has_visa_been_cancelled_or_revoked: Optional[VisaCancelledOrRevoked] = None

class RefusalInfo(BaseModel):
    """Refusal information."""
    answer: str = Field(..., description="Yes/No answer")
    explanation: Optional[str] = Field(None, description="Explanation")

class PreviousUSTravel(BaseModel):
    """Previous US travel information."""
    has_ever_been_in_us: str = Field(default="No")
    previous_visits: List[PreviousVisit] = Field(default_factory=list)
    has_ever_held_us_license: str = Field(default="No")
    drivers_licenses: List[DriversLicense] = Field(default_factory=list)
    has_ever_been_issued_us_visa: str = Field(default="No")
    previous_visa_info: List[PreviousVisaInfo] = Field(default_factory=list)
    has_ever_been_refused_admission_or_visa: Optional[RefusalInfo] = None
    has_immigrant_petition_been_filed: Optional[RefusalInfo] = None
    has_ever_been_denied_travel_authorization: Optional[RefusalInfo] = None

# US Contact
class USContact(BaseModel):
    """US contact information."""
    contact_person_surnames: str = Field(..., min_length=1, max_length=100)
    contact_person_given_names: str = Field(..., min_length=1, max_length=100)
    organization_name: Optional[str] = Field(None, description="Organization name")
    relationship_to_you: str = Field(..., description="Relationship to applicant")
    address: USAddress
    phone: str = Field(..., description="Phone number")
    email: EmailStr = Field(..., description="Email address")

# Sponsor Info
class PersonSponsor(BaseModel):
    """Person sponsor information (for 'Other Person' payer type)."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., description="Relationship to applicant")
    address: Address  # Changed from USAddress to support international addresses
    phone: str = Field(..., description="Phone number")
    email: EmailStr = Field(..., description="Email address")

class CompanySponsor(BaseModel):
    """Company sponsor information (for 'Other Company/Organization' payer type)."""
    company_name: str = Field(..., min_length=1, max_length=200)
    contact_person_surnames: str = Field(..., min_length=1, max_length=100)
    contact_person_given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., description="Relationship to applicant")
    address: USAddress
    phone: str = Field(..., description="Phone number")
    email: EmailStr = Field(..., description="Email address")

class USPetitionerSponsor(BaseModel):
    """US Petitioner sponsor information (for 'U.S. Petitioner' payer type)."""
    petitioner_surnames: str = Field(..., min_length=1, max_length=100)
    petitioner_given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., description="Relationship to applicant")
    address: USAddress
    phone: str = Field(..., description="Phone number")
    email: EmailStr = Field(..., description="Email address")

class EmployerSponsor(BaseModel):
    """Employer sponsor information (for 'Present Employer' or 'Employer in the U.S.' payer types)."""
    employer_name: str = Field(..., min_length=1, max_length=200)
    contact_person_surnames: Optional[str] = Field(None, max_length=100)
    contact_person_given_names: Optional[str] = Field(None, max_length=100)
    address: USAddress
    phone: str = Field(..., description="Phone number")
    email: EmailStr = Field(..., description="Email address")

class SponsorInfo(BaseModel):
    """Sponsor information based on person_entity_paying."""
    person_sponsor: Optional[PersonSponsor] = None
    company_sponsor: Optional[CompanySponsor] = None
    us_petitioner_sponsor: Optional[USPetitionerSponsor] = None
    employer_sponsor: Optional[EmployerSponsor] = None

# Family Info
class FamilyMember(BaseModel):
    """Family member information."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    date_of_birth: date
    is_in_the_us: str = Field(default="No")
    status: str = Field(..., description="Status (U.S. CITIZEN, NONIMMIGRANT, etc.)")

class ImmediateRelative(BaseModel):
    """Immediate relative information."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., description="Relationship to applicant")
    status: str = Field(..., description="Status")

class FamilyInfo(BaseModel):
    """Family information."""
    father: FamilyMember
    mother: FamilyMember
    has_immediate_relatives_in_us: str = Field(default="No")
    immediate_relatives: List[ImmediateRelative] = Field(default_factory=list)

# Work and Education
class WorkEducationAddress(BaseModel):
    """Work/Education address information."""
    street_line1: str = Field(..., min_length=1, max_length=200, alias="address_line1")
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None, max_length=100)
    postal_zone_zip_code: Optional[str] = Field(None, max_length=20, alias="postal_code", description="Postal/ZIP code (if applicable)")
    country_region: str = Field(None, max_length=100, alias="country")
    
    @validator('street_line1', pre=True)
    def validate_street_line1(cls, v):
        """Validate street address line 1 according to DS-160 requirements."""
        return validate_ds160_street_address(v)
    
    class Config:
        populate_by_name = True  # Allow both field name and alias

class PresentWork(BaseModel):
    """Present work information."""
    primary_occupation: str = Field(..., description="Primary occupation")
    occupation_specification: Optional[str] = Field(None, description="Occupation specification when 'Other' is selected")
    employer_or_school_name: str = Field(..., description="Employer or school name")
    address: WorkEducationAddress
    start_date: date = Field(..., description="Start date")
    monthly_income_local: Optional[str] = Field(None, description="Monthly income in local currency")
    duties: str = Field(..., description="Job duties")
    
    @validator('primary_occupation', pre=True)
    def normalize_occupation(cls, v):
        """Automatically map modern occupations to DS-160 valid options."""
        if isinstance(v, str):
            return PrimaryOccupation.get_best_match(v)
        return v

class PreviousWork(BaseModel):
    """Previous work information."""
    employer_name: str = Field(..., description="Employer name")
    address: WorkEducationAddress
    telephone_number: str = Field(..., description="Telephone number")
    job_title: str = Field(..., description="Job title")
    supervisor_surnames: str = Field(..., description="Supervisor surnames")
    supervisor_given_names: str = Field(..., description="Supervisor given names")
    employment_date_from: date = Field(..., description="Employment start date")
    employment_date_to: date = Field(..., description="Employment end date")
    duties: str = Field(..., description="Job duties")

class PreviousEducation(BaseModel):
    """Previous education information."""
    institution_name: str = Field(..., description="Institution name")
    address: WorkEducationAddress
    course_of_study: str = Field(..., description="Course of study")
    date_of_attendance_from: date = Field(..., description="Attendance start date")
    date_of_attendance_to: date = Field(..., description="Attendance end date")

class YesNoAnswer(BaseModel):
    """Yes/No answer with explanation."""
    answer: str = Field(..., description="Yes/No answer")
    explanation: Optional[str] = Field(None, description="Explanation if Yes")

class CharitableOrganization(BaseModel):
    """Charitable organization information."""
    answer: str = Field(..., description="Yes/No answer")
    organization_name: Optional[str] = Field(None, description="Organization name")

class MilitaryService(BaseModel):
    """Military service information."""
    answer: str = Field(..., description="Yes/No answer")
    country: Optional[str] = Field(None, description="Country of service")
    branch_of_service: Optional[str] = Field(None, description="Branch of service")
    rank_position: Optional[str] = Field(None, description="Rank or position")
    military_specialty: Optional[str] = Field(None, description="Military specialty")
    date_of_service_from: Optional[date] = Field(None, description="Service start date")
    date_of_service_to: Optional[date] = Field(None, description="Service end date")

class AdditionalInfo(BaseModel):
    """Additional information."""
    clan_or_tribe_name: Optional[str] = Field(None, description="Clan or tribe name")
    languages_spoken: List[str] = Field(default_factory=list)
    countries_visited_last_five_years: List[str] = Field(default_factory=list)
    charitable_organizations_worked_for: Optional[CharitableOrganization] = None
    specialized_skills: Optional[YesNoAnswer] = None
    has_served_in_military: Optional[MilitaryService] = None
    has_been_in_rebel_group: Optional[YesNoAnswer] = None

class WorkAndEducation(BaseModel):
    """Work and education information."""
    present: PresentWork
    previous_work: List[PreviousWork] = Field(default_factory=list)
    previous_education: List[PreviousEducation] = Field(default_factory=list)
    additional_info: Optional[AdditionalInfo] = None

# Security and Background
class SecurityPart1(BaseModel):
    """Security questions part 1 - Medical and Health."""
    has_communicable_disease: Optional[YesNoAnswer] = None
    has_mental_or_physical_disorder: Optional[YesNoAnswer] = None
    is_drug_abuser_or_addict: Optional[YesNoAnswer] = None

class SecurityPart2(BaseModel):
    """Security questions part 2 - Criminal."""
    has_been_arrested: Optional[YesNoAnswer] = None
    has_violated_controlled_substance_laws: Optional[YesNoAnswer] = None
    engaged_in_prostitution: Optional[YesNoAnswer] = None
    engaged_in_money_laundering: Optional[YesNoAnswer] = None
    committed_human_trafficking: Optional[YesNoAnswer] = None
    aided_human_trafficking: Optional[YesNoAnswer] = None
    is_family_of_human_trafficker: Optional[YesNoAnswer] = None

class SecurityPart3(BaseModel):
    """Security questions part 3 - Security."""
    engaged_in_espionage_or_sabotage: Optional[YesNoAnswer] = None
    engaged_in_terrorist_activities: Optional[YesNoAnswer] = None
    provided_support_to_terrorists: Optional[YesNoAnswer] = None
    is_member_of_terrorist_organization: Optional[YesNoAnswer] = None
    participated_in_genocide: Optional[YesNoAnswer] = None
    participated_in_torture: Optional[YesNoAnswer] = None
    participated_in_extrajudicial_killings: Optional[YesNoAnswer] = None
    violated_religious_freedoms: Optional[YesNoAnswer] = None
    involved_in_forced_population_control: Optional[YesNoAnswer] = None
    involved_in_coercive_organ_transplantation: Optional[YesNoAnswer] = None

class SecurityPart4(BaseModel):
    """Security questions part 4 - Immigration Violations."""
    sought_visa_by_fraud: Optional[YesNoAnswer] = None
    failed_to_attend_removal_hearings: Optional[YesNoAnswer] = None
    been_unlawfully_present_or_overstayed: Optional[YesNoAnswer] = None
    been_removed_or_deported: Optional[YesNoAnswer] = None

class SecurityPart5(BaseModel):
    """Security questions part 5 - Miscellaneous."""
    withheld_custody_of_us_citizen_child: Optional[YesNoAnswer] = None
    voted_in_us_illegally: Optional[YesNoAnswer] = None
    renounced_us_citizenship_for_tax: Optional[YesNoAnswer] = None
    attended_public_school_on_student_visa_without_reimbursement: Optional[YesNoAnswer] = None

class SecurityAndBackground(BaseModel):
    """Security and background information."""
    part1_medical_and_health: Optional[SecurityPart1] = None
    part2_criminal: Optional[SecurityPart2] = None
    part3_security: Optional[SecurityPart3] = None
    part4_immigration_violations: Optional[SecurityPart4] = None
    part5_miscellaneous: Optional[SecurityPart5] = None

# Main Data Model
class DS160Data(BaseModel):
    """Complete DS-160 form data."""
    application_info: ApplicationInfo = Field(default_factory=ApplicationInfo, alias="applicationInfo")
    personal_info: PersonalInfo = Field(alias="personalInfo")
    nationality_and_residence: NationalityAndResidence = Field(alias="nationalityAndResidence")
    passport_info: PassportInfo = Field(alias="passportInfo")
    contact_info: ContactInfo = Field(alias="contactInfo")
    travel_info: TravelInfo = Field(alias="travelInfo")
    travel_companions: Optional[TravelCompanions] = Field(None, alias="travelCompanions")
    previous_us_travel: Optional[PreviousUSTravel] = Field(None, alias="previousUSTravel")
    us_contact: USContact = Field(alias="usContact")
    sponsor_info: Optional[SponsorInfo] = Field(None, alias="sponsorInfo")
    family_info: FamilyInfo = Field(alias="familyInfo")
    work_and_education: WorkAndEducation = Field(alias="workAndEducation")
    security_and_background: Optional[SecurityAndBackground] = Field(None, alias="securityAndBackground")
    
    class Config:
        populate_by_name = True  # Allow both field name and alias
        json_encoders = {
            date: lambda v: v.isoformat() if v else None,
            datetime: lambda v: v.isoformat() if v else None,
        }

class DataLoader:
    """Data loader and validator for DS-160 form data."""
    
    def __init__(self, file_path: str = None):
        """Initialize with data file path."""
        self.file_path = Path(file_path) if file_path else None
        self.data: Optional[DS160Data] = None
        self._raw_data: Optional[Dict[str, Any]] = None
    
    @classmethod
    def from_dict(cls, data_dict: Dict[str, Any]) -> 'DataLoader':
        """Create DataLoader from dictionary (for testing)."""
        loader = cls()
        loader._raw_data = data_dict
        return loader
    
    async def load_data(self) -> DS160Data:
        """Load and validate data from JSON file or raw data."""
        try:
            if self._raw_data is not None:
                # Use provided raw data (for testing)
                raw_data = self._raw_data.copy()
            else:
                # Load from file
                if not self.file_path or not self.file_path.exists():
                    raise FileNotFoundError(f"Data file not found: {self.file_path}")
                
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
            
            # Convert camelCase to snake_case for field names (except for models with explicit aliases)
            raw_data = self._convert_keys_to_snake_case(raw_data)
            
            # Fix address field mappings for education/work data
            raw_data = self._fix_address_mappings(raw_data)
            
            # Convert date strings to date objects
            raw_data = self._convert_dates(raw_data)
            
            # Add default security data if missing
            raw_data = self._ensure_security_defaults(raw_data)
            
            # Validate and create DS160Data object
            self.data = DS160Data(**raw_data)
            return self.data
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in data file: {e}")
        except Exception as e:
            raise ValueError(f"Error validating data: {e}")
    
    def load_data_sync(self) -> DS160Data:
        """Synchronous version of load_data for unit tests."""
        try:
            if self._raw_data is not None:
                # Use provided raw data (for testing)
                raw_data = self._raw_data.copy()
            else:
                # Load from file
                if not self.file_path or not self.file_path.exists():
                    raise FileNotFoundError(f"Data file not found: {self.file_path}")
                
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
            
            # Convert camelCase to snake_case for field names (except for models with explicit aliases)
            raw_data = self._convert_keys_to_snake_case(raw_data)
            
            # Fix address field mappings for education/work data
            raw_data = self._fix_address_mappings(raw_data)
            
            # Convert date strings to date objects
            raw_data = self._convert_dates(raw_data)
            
            # Add default security data if missing
            raw_data = self._ensure_security_defaults(raw_data)
            
            # Validate and create DS160Data object
            self.data = DS160Data(**raw_data)
            return self.data
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in data file: {e}")
        except Exception as e:
            raise ValueError(f"Error validating data: {e}")
    
    def _convert_keys_to_snake_case(self, data: Any) -> Any:
        """Convert camelCase keys to snake_case recursively."""
        if isinstance(data, dict):
            converted = {}
            for key, value in data.items():
                # Special handling for specific keys
                if key == 'locationsToVisitInUS':
                    snake_key = 'locations_to_visit_in_us'
                elif key == 'usStayAddress':
                    snake_key = 'us_stay_address'
                elif key == 'personEntityPaying':
                    snake_key = 'person_entity_paying'
                elif key == 'previousUSTravel':
                    snake_key = 'previous_us_travel'
                elif key == 'isInTheUS':
                    snake_key = 'is_in_the_us'
                elif key == 'hasEverBeenInUS':
                    snake_key = 'has_ever_been_in_us'
                elif key == 'hasImmediateRelativesInUS':
                    snake_key = 'has_immediate_relatives_in_us'
                elif key == 'withheldCustodyOfUSCitizenChild':
                    snake_key = 'withheld_custody_of_us_citizen_child'
                elif key == 'votedInUSIllegally':
                    snake_key = 'voted_in_us_illegally'
                elif key == 'renouncedUSCitizenshipForTax':
                    snake_key = 'renounced_us_citizenship_for_tax'
                elif key == 'attendedPublicSchoolOnStudentVisaWithoutReimbursement':
                    snake_key = 'attended_public_school_on_student_visa_without_reimbursement'
                else:
                    # Convert camelCase to snake_case
                    snake_key = ''.join(['_' + char.lower() if char.isupper() else char for char in key]).lstrip('_')
                converted[snake_key] = self._convert_keys_to_snake_case(value)
            return converted
        elif isinstance(data, list):
            return [self._convert_keys_to_snake_case(item) for item in data]
        else:
            return data
    
    def _convert_dates(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert date strings to date objects recursively."""
        if isinstance(data, dict):
            converted = {}
            for key, value in data.items():
                if isinstance(value, str) and self._is_date_field(key):
                    try:
                        converted[key] = datetime.fromisoformat(value).date()
                    except ValueError:
                        converted[key] = value
                elif isinstance(value, (dict, list)):
                    converted[key] = self._convert_dates(value)
                else:
                    converted[key] = value
            return converted
        elif isinstance(data, list):
            return [self._convert_dates(item) for item in data]
        else:
            return data
    
    def _is_date_field(self, field_name: str) -> bool:
        """Check if field name indicates a date field."""
        date_indicators = [
            'date', 'start_date', 'end_date', 'birth', 'issuance', 'expiration', 
            'arrival', 'departure', 'employment_date', 'attendance', 'service'
        ]
        return any(indicator in field_name.lower() for indicator in date_indicators)
    
    def _fix_address_mappings(self, data: Any) -> Any:
        """Fix address field mappings for WorkEducationAddress models."""
        if isinstance(data, dict):
            converted = {}
            for key, value in data.items():
                if key == 'address' and isinstance(value, dict):
                    # Fix address field mappings
                    fixed_address = {}
                    for addr_key, addr_value in value.items():
                        if addr_key == 'address_line1':
                            fixed_address['street_line1'] = addr_value
                        elif addr_key == 'postal_code':
                            fixed_address['postal_zone_zip_code'] = addr_value
                        elif addr_key == 'country':
                            fixed_address['country_region'] = addr_value
                        elif addr_key == 'state_province' and (addr_value is None or addr_value == ''):
                            # Handle empty state_province by setting a default
                            fixed_address['state_province'] = 'N/A'
                        else:
                            fixed_address[addr_key] = addr_value
                    converted[key] = fixed_address
                else:
                    converted[key] = self._fix_address_mappings(value)
            return converted
        elif isinstance(data, list):
            return [self._fix_address_mappings(item) for item in data]
        else:
            return data
    
    def _ensure_security_defaults(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure security_and_background has default 'No' answers for all questions."""
        if 'security_and_background' not in data or data['security_and_background'] is None:
            # Create complete default security data with all "No" answers
            default_security = {
                "part1_medical_and_health": {
                    "has_communicable_disease": {"answer": "No"},
                    "has_mental_or_physical_disorder": {"answer": "No"},
                    "is_drug_abuser_or_addict": {"answer": "No"}
                },
                "part2_criminal": {
                    "has_been_arrested": {"answer": "No"},
                    "has_violated_controlled_substance_laws": {"answer": "No"},
                    "engaged_in_prostitution": {"answer": "No"},
                    "engaged_in_money_laundering": {"answer": "No"},
                    "committed_human_trafficking": {"answer": "No"},
                    "aided_human_trafficking": {"answer": "No"},
                    "is_family_of_human_trafficker": {"answer": "No"}
                },
                "part3_security": {
                    "engaged_in_espionage_or_sabotage": {"answer": "No"},
                    "engaged_in_terrorist_activities": {"answer": "No"},
                    "provided_support_to_terrorists": {"answer": "No"},
                    "is_member_of_terrorist_organization": {"answer": "No"},
                    "participated_in_genocide": {"answer": "No"},
                    "participated_in_torture": {"answer": "No"},
                    "participated_in_extrajudicial_killings": {"answer": "No"},
                    "violated_religious_freedoms": {"answer": "No"},
                    "involved_in_forced_population_control": {"answer": "No"},
                    "involved_in_coercive_organ_transplantation": {"answer": "No"}
                },
                "part4_immigration_violations": {
                    "sought_visa_by_fraud": {"answer": "No"},
                    "failed_to_attend_removal_hearings": {"answer": "No"},
                    "been_unlawfully_present_or_overstayed": {"answer": "No"},
                    "been_removed_or_deported": {"answer": "No"}
                },
                "part5_miscellaneous": {
                    "withheld_custody_of_us_citizen_child": {"answer": "No"},
                    "voted_in_us_illegally": {"answer": "No"},
                    "renounced_us_citizenship_for_tax": {"answer": "No"},
                    "attended_public_school_on_student_visa_without_reimbursement": {"answer": "No"}
                }
            }
            
            data['security_and_background'] = default_security
            print("✅ Added default security data with all 'No' answers")
        
        return data
    
    def validate_data(self) -> List[str]:
        """Validate loaded data and return list of issues."""
        if not self.data:
            return ["No data loaded"]
        
        issues = []
        
        # Check required fields
        if not self.data.personal_info.surnames:
            issues.append("Surnames are required")
        
        if not self.data.personal_info.given_names:
            issues.append("Given names are required")
        
        # Check passport validity
        if self.data.passport_info.expiration_date <= date.today():
            issues.append("Passport is expired or expires today")
        
        # Check travel dates
        if self.data.travel_info.arrival_date <= date.today():
            issues.append("Intended arrival date must be in the future")
        
        return issues

def create_example_data() -> Dict[str, Any]:
    """Create example data structure based on the comprehensive format."""
    return {
        "applicationInfo": {
            "formType": "DS-160",
            "applicationId": "AA00EVJQY3"
        },
        "personalInfo": {
            "surnames": "IVANOV IVANOVICH",
            "givenNames": "IVANOV IVAN IVANOVICH",
            "fullNameNativeAlphabet": "ИВАНОВ ИВАН ИВАНОВИЧ",
            "otherNamesUsed": [
                {
                    "otherSurnames": "ПЕТРОВ",
                    "otherGivenNames": "ВАСИЛЬКОВ"
                }
            ],
            "telecodeRepresentsName": "No",
            "sex": "MALE",
            "maritalStatus": "SINGLE",
            "dateOfBirth": "1999-06-12",
            "placeOfBirth": {
                "city": "ASTANA",
                "stateProvince": "AKMOLA",
                "countryRegion": "KAZAKHSTAN"
            }
        },
        "nationalityAndResidence": {
            "countryOfOrigin": "KAZAKHSTAN",
            "otherNationalities": [
                {
                    "country": "TURKEY",
                    "hasPassport": "Yes",
                    "passportNumber": "N12345678"
                }
            ],
            "isPermanentResidentOfOtherCountry": "Yes",
            "permanentResidentCountries": [
                "BAHAMAS"
            ],
            "nationalIdentificationNumber": "12345",
            "usSocialSecurityNumber": "***********",
            "usTaxpayerIdNumber": "345345"
        },
        "passportInfo": {
            "passportType": "REGULAR",
            "passportNumber": "N435RTB",
            "passportBookNumber": "GYUGKJ",
            "issuingCountry": "KAZAKHSTAN",
            "issuingCity": "ASTANA",
            "issuingStateProvince": "AKMOLA",
            "issuanceDate": "2025-06-10",
            "expirationDate": "2030-05-04",
            "hasLostOrStolenPassport": {
                "answer": "Yes",
                "lostPassportNumber": "2345678",
                "lostPassportIssuingCountry": "KAZAKHSTAN",
                "explanation": "DSA"
            }
        },
        "contactInfo": {
            "mailingAddress": {
                "streetLine1": "MANGILIK EL 2",
                "city": "ASTANA",
                "stateProvince": "AKMOLA",
                "postalZoneZipCode": "910000",
                "countryRegion": "KAZAKHSTAN"
            },
            "phoneNumbers": {
                "primary": "87017023344",
                "secondary": "87017023342",
                "work": "87017023343",
                "other": "87017023346"
            },
            "emailAddresses": {
                "primary": "<EMAIL>",
                "additional": "<EMAIL>"
            },
            "socialMedia": [
                {
                    "platform": "INSTAGRAM",
                    "identifier": "***********"
                },
                {
                    "platform": "INSTAGRAM",
                    "identifier": "IDH23873022"
                }
            ]
        },
        "travelInfo": {
            "purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)",
            "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)",
            "hasSpecificTravelPlans": "Yes",
            "arrivalDate": "2025-08-18",
            "arrivalFlight": "UA101",
            "arrivalCity": "NEW YORK",
            "departureDate": "2025-11-27",
            "departureFlight": "KC122",
            "departureCity": "ASTANA",
            "locationsToVisitInUS": [
                "NEW YORK"
            ],
            "usStayAddress": {
                "streetLine1": "STREET, 65",
                "city": "NEW YORK",
                "state": "NEW YORK",
                "zipCode": "12345"
            },
            "personEntityPaying": "SELF"
        },
        "travelCompanions": {
            "areOtherPersonsTraveling": "Yes",
            "personsTraveling": [
                {
                    "surnames": "IVANOV IVAN",
                    "givenNames": "IVANOV IVAN",
                    "relationship": "CHILD"
                }
            ],
            "isTravelingAsPartOfGroup": "Yes",
            "groupName": "TEST"
        },
        "previousUSTravel": {
            "hasEverBeenInUS": "Yes",
            "previousVisits": [
                {
                    "dateArrived": "2024-02-19",
                    "lengthOfStay": "60",
                    "unitOfStay": "DAY(S)"
                }
            ],
            "hasEverHeldUSLicense": "Yes",
            "driversLicenses": [
                {
                    "licenseNumber": "********",
                    "issuingState": "NEW YORK"
                }
            ],
            "hasEverBeenIssuedUSVisa": "Yes",
            "previousVisaInfo": {
                "dateLastVisaIssued": "2023-07-13",
                "visaNumber": "12345678",
                "isApplyingForSameType": "Yes",
                "isApplyingInSameCountry": "Yes",
                "hasBeenTenPrinted": "Yes",
                "hasVisaBeenLostOrStolen": {
                    "answer": "Yes",
                    "yearLost": "2019",
                    "explanation": "FDSFDSFDS"
                },
                "hasVisaBeenCancelledOrRevoked": {
                    "answer": "Yes",
                    "explanation": "FJRJEL"
                }
            },
            "hasEverBeenRefusedAdmissionOrVisa": {
                "answer": "Yes",
                "explanation": "FJEFKR"
            },
            "hasImmigrantPetitionBeenFiled": {
                "answer": "Yes",
                "explanation": "FRMKEL"
            },
            "hasEverBeenDeniedTravelAuthorization": {
                "answer": "No",
                "explanation": ""
            }
        },
        "usContact": {
            "contactPersonSurnames": "SMITH",
            "contactPersonGivenNames": "TRAVER",
            "organizationName": "CLAUD",
            "relationshipToYou": "SPOUSE",
            "address": {
                "streetLine1": "STREET 65",
                "city": "NEW YORK",
                "state": "NEW YORK",
                "zipCode": "55555"
            },
            "phone": "87017023344",
            "email": "<EMAIL>"
        },
        "familyInfo": {
            "father": {
                "surnames": "HIT",
                "givenNames": "JUAN MIGUEL",
                "dateOfBirth": "1970-03-24",
                "isInTheUS": "Yes",
                "status": "U.S. CITIZEN"
            },
            "mother": {
                "surnames": "MOTHER NAMES",
                "givenNames": "JUANITA MIGUEL",
                "dateOfBirth": "1980-09-04",
                "isInTheUS": "Yes",
                "status": "NONIMMIGRANT"
            },
            "hasImmediateRelativesInUS": "Yes",
            "immediateRelatives": [
                {
                    "surnames": "HIT",
                    "givenNames": "HEU UAH",
                    "relationship": "SIBLING",
                    "status": "NONIMMIGRANT"
                }
            ]
        },
        "workAndEducation": {
            "present": {
                "primaryOccupation": "COMPUTER SCIENCE",
                "employerOrSchoolName": "TREST",
                "address": {
                    "streetLine1": "MANGOLIK EL",
                    "city": "ASTANA",
                    "stateProvince": "AKMOLA",
                    "postalZoneZipCode": "010000",
                    "countryRegion": "KAZAKHSTAN"
                },
                "startDate": "2024-03-02",
                "monthlyIncomeLocal": "600000",
                "duties": "DFSAFDS"
            },
            "previousWork": [
                {
                    "employerName": "QWERTYUIO",
                    "address": {
                        "streetLine1": "MANGILIK EL",
                        "city": "ASTANA",
                        "stateProvince": "AKMOLA",
                        "postalZoneZipCode": "010000",
                        "countryRegion": "KAZAKHSTAN"
                    },
                    "telephoneNumber": "87017023344",
                    "jobTitle": "IT",
                    "supervisorSurnames": "HIT",
                    "supervisorGivenNames": "TREWIA",
                    "employmentDateFrom": "2022-07-02",
                    "employmentDateTo": "2024-02-02",
                    "duties": "CDUKSPOOLB"
                }
            ],
            "previousEducation": [
                {
                    "institutionName": "jet",
                    "address": {
                        "streetLine1": "MANGILIK EL",
                        "city": "ASTANA",
                        "stateProvince": "AKMOLA",
                        "postalZoneZipCode": "010000",
                        "countryRegion": "KAZAKHSTAN"
                    },
                    "courseOfStudy": "H",
                    "dateOfAttendanceFrom": "2022-03-01",
                    "dateOfAttendanceTo": "2025-04-03"
                }
            ],
            "additionalInfo": {
                "clanOrTribeName": "TEDSTY",
                "languagesSpoken": [
                    "KAZAKH",
                    "ENGLISH"
                ],
                "countriesVisitedLastFiveYears": [
                    "AMERICAN SAMOA"
                ],
                "charitableOrganizationsWorkedFor": {
                    "answer": "Yes",
                    "organizationName": "TEDFS"
                },
                "specializedSkills": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "hasServedInMilitary": {
                    "answer": "Yes",
                    "country": "KAZAKHSTAN",
                    "branchOfService": "DVD",
                    "rankPosition": "CAPRAL",
                    "militarySpecialty": "CAPRAL",
                    "dateOfServiceFrom": "2023-04-03",
                    "dateOfServiceTo": "2024-03-02"
                },
                "hasBeenInRebelGroup": {
                    "answer": "Yes",
                    "explanation": "TEGRE"
                }
            }
        },
        "securityAndBackground": {
            "part1MedicalAndHealth": {
                "hasCommunicableDisease": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "hasMentalOrPhysicalDisorder": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "isDrugAbuserOrAddict": {
                    "answer": "Yes",
                    "explanation": "TEST"
                }
            },
            "part2Criminal": {
                "hasBeenArrested": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "hasViolatedControlledSubstanceLaws": {
                    "answer": "Yes",
                    "explanation": "GTEST"
                },
                "engagedInProstitution": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "engagedInMoneyLaundering": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "committedHumanTrafficking": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "aidedHumanTrafficking": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "isFamilyOfHumanTrafficker": {
                    "answer": "Yes",
                    "explanation": "TEST"
                }
            },
            "part3Security": {
                "engagedInEspionageOrSabotage": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "engagedInTerroristActivities": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "providedSupportToTerrorists": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "isMemberOfTerroristOrganization": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "participatedInGenocide": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "participatedInTorture": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "participatedInExtrajudicialKillings": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "violatedReligiousFreedoms": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "involvedInForcedPopulationControl": {
                    "answer": "Yes",
                    "explanation": "TTEST"
                },
                "involvedInCoerciveOrganTransplantation": {
                    "answer": "Yes",
                    "explanation": "TEST"
                }
            },
            "part4ImmigrationViolations": {
                "soughtVisaByFraud": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "failedToAttendRemovalHearings": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "beenUnlawfullyPresentOrOverstayed": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "beenRemovedOrDeported": {
                    "answer": "Yes",
                    "explanation": "TEST"
                }
            },
            "part5Miscellaneous": {
                "withheldCustodyOfUSCitizenChild": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "votedInUSIllegally": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "renouncedUSCitizenshipForTax": {
                    "answer": "Yes",
                    "explanation": "TEST"
                },
                "attendedPublicSchoolOnStudentVisaWithoutReimbursement": {
                    "answer": "Yes",
                    "explanation": "TEST"
                }
            }
        }
    } 