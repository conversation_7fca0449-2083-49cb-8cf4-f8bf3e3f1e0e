# DS-160 Data Examples and Scenarios

Complete reference for DS-160 application data structures with real-world examples.

## Overview

This document provides comprehensive examples of all possible DS-160 form scenarios to help developers create test cases and understand data structure requirements.

---

## Personal Info Examples

### 1. Basic Single Person (Never Married)
```json
{
  "personalInfo": {
    "surnames": "IVANOV",
    "givenNames": "IVAN IVANOVICH", 
    "fullNameNativeAlphabet": "ИВАНОВ ИВАН ИВАНОВИЧ",
    "otherNamesUsed": [],
    "telecodeRepresentsName": "No",
    "sex": "MALE",
    "maritalStatus": "NEVER MARRIED",
    "dateOfBirth": "1990-06-15",
    "placeOfBirth": {
      "city": "ALMATY",
      "stateProvince": "ALMATY REGION", 
      "countryRegion": "KAZAKHSTAN"
    }
  }
}
```

### 2. Married with Current Spouse
```json
{
  "personalInfo": {
    "maritalStatus": "MARRIED",
    "currentSpouse": {
      "surnames": "IVANO<PERSON>",
      "givenNames": "ELENA PETROVNA",
      "dateOfBirth": "1992-03-20",
      "nationality": "KAZAKHSTAN",
      "placeOfBirth": {
        "city": "ALMATY",
        "countryRegion": "KAZAKHSTAN"
      },
      "address": {
        "streetLine1": "AL-FARABI AVENUE 123",
        "city": "ALMATY",
        "countryRegion": "KAZAKHSTAN"
      },
      "dateOfMarriage": "2015-08-15"
    }
  }
}
```

### 3. Divorced with Former Spouse
```json
{
  "personalInfo": {
    "maritalStatus": "DIVORCED",
    "formerSpouses": [
      {
        "surnames": "PETROV",
        "givenNames": "SERGEI ALEXANDROVICH", 
        "dateOfBirth": "1985-07-10",
        "nationality": "RUSSIA",
        "placeOfBirth": {
          "city": "MOSCOW",
          "countryRegion": "RUSSIA"
        },
        "dateOfMarriage": "2010-05-15",
        "dateMarriageEnded": "2018-09-20",
        "howMarriageEnded": "DIVORCE",
        "countryMarriageEnded": "KAZAKHSTAN"
      }
    ]
  }
}
```

### 4. Widowed with Deceased Spouse
```json
{
  "personalInfo": {
    "maritalStatus": "WIDOWED",
    "deceasedSpouse": {
      "surnames": "PETROV",
      "givenNames": "MIKHAIL ALEXANDROVICH",
      "dateOfBirth": "1972-04-15",
      "placeOfBirth": {
        "city": "MOSCOW",
        "stateProvince": "MOSCOW REGION",
        "countryRegion": "RUSSIA"
      },
      "nationality": "RUSSIA", 
      "dateOfMarriage": "1998-08-20",
      "dateOfDeath": "2022-06-10",
      "placeOfDeath": {
        "city": "MOSCOW",
        "stateProvince": "MOSCOW REGION",
        "countryRegion": "RUSSIA"
      }
    }
  }
}
```

### 5. Minor Under 14
```json
{
  "personalInfo": {
    "surnames": "JOHNSON",
    "givenNames": "EMMA KATE",
    "sex": "FEMALE",
    "maritalStatus": "SINGLE",
    "dateOfBirth": "2015-03-10",
    "placeOfBirth": {
      "city": "NEW YORK", 
      "stateProvince": "NEW YORK",
      "countryRegion": "UNITED STATES"
    }
  }
}
```

---

## Nationality and Residence Examples

### 1. Single Nationality
```json
{
  "nationalityAndResidence": {
    "countryOfOrigin": "KAZAKHSTAN",
    "otherNationalities": [],
    "isPermanentResidentOfOtherCountry": "No",
    "permanentResidentCountries": []
  }
}
```

### 2. Multiple Nationalities (Dual/Triple Citizenship)
```json
{
  "nationalityAndResidence": {
    "countryOfOrigin": "KAZAKHSTAN",
    "otherNationalities": [
      {
        "country": "TURKEY",
        "hasPassport": "Yes", 
        "passportNumber": "N12345678"
      },
      {
        "country": "RUSSIA",
        "hasPassport": "Yes",
        "passportNumber": "*********0"
      }
    ],
    "isPermanentResidentOfOtherCountry": "No"
  }
}
```

### 3. Permanent Resident of Multiple Countries
```json
{
  "nationalityAndResidence": {
    "countryOfOrigin": "KAZAKHSTAN", 
    "otherNationalities": [],
    "isPermanentResidentOfOtherCountry": "Yes",
    "permanentResidentCountries": ["UNITED ARAB EMIRATES", "LITHUANIA"]
  }
}
```

### 4. Complex CIS Scenario
```json
{
  "nationalityAndResidence": {
    "countryOfOrigin": "RUSSIA",
    "otherNationalities": [
      {
        "country": "BELARUS",
        "hasPassport": "No"
      }
    ],
    "isPermanentResidentOfOtherCountry": "Yes", 
    "permanentResidentCountries": ["KAZAKHSTAN"]
  }
}
```

---

## Work and Education Examples

### 1. Current Student (No Work History)
```json
{
  "workAndEducation": {
    "present": {
      "primaryOccupation": "STUDENT",
      "employerOrSchoolName": "KAZAKH NATIONAL UNIVERSITY",
      "address": {
        "streetLine1": "AL-FARABI AVENUE 71",
        "city": "ALMATY",
        "stateProvince": "ALMATY REGION",
        "postalZoneZipCode": "050040",
        "countryRegion": "KAZAKHSTAN"
      },
      "startDate": "2020-09-01",
      "monthlyIncomeLocal": "0",
      "duties": "COMPUTER SCIENCE STUDIES"
    },
    "previousWork": [],
    "previousEducation": [
      {
        "institutionName": "ALMATY LYCEUM #165",
        "address": {
          "streetLine1": "ABYLAI KHAN AVENUE 85",
          "city": "ALMATY", 
          "stateProvince": "ALMATY REGION",
          "postalZoneZipCode": "050004",
          "countryRegion": "KAZAKHSTAN"
        },
        "courseOfStudy": "GENERAL EDUCATION",
        "dateOfAttendanceFrom": "2015-09-01",
        "dateOfAttendanceTo": "2020-06-30"
      }
    ]
  }
}
```

### 2. Working Professional with Career Progression
```json
{
  "workAndEducation": {
    "present": {
      "primaryOccupation": "COMPUTER SCIENCE",
      "employerOrSchoolName": "EPAM KAZAKHSTAN",
      "address": {
        "streetLine1": "AL-FARABI AVENUE 77/8",
        "city": "ALMATY",
        "stateProvince": "ALMATY REGION", 
        "postalZoneZipCode": "050040",
        "countryRegion": "KAZAKHSTAN"
      },
      "startDate": "2022-03-01",
      "monthlyIncomeLocal": "800000",
      "duties": "SENIOR SOFTWARE ENGINEER - FULL STACK DEVELOPMENT"
    },
    "previousWork": [
      {
        "employerName": "JUSAN BANK",
        "address": {
          "streetLine1": "AL-FARABI AVENUE 36",
          "city": "ALMATY",
          "stateProvince": "ALMATY REGION",
          "postalZoneZipCode": "050059", 
          "countryRegion": "KAZAKHSTAN"
        },
        "telephoneNumber": "******-244-5555",
        "jobTitle": "SOFTWARE DEVELOPER", 
        "supervisorSurnames": "NAZARBAYEV",
        "supervisorGivenNames": "AIBEK NURLANOVICH",
        "employmentDateFrom": "2020-06-01",
        "employmentDateTo": "2022-02-28",
        "duties": "BANKING SYSTEMS DEVELOPMENT AND MAINTENANCE"
      },
      {
        "employerName": "TECH SOLUTIONS KZ",
        "address": {
          "streetLine1": "SATPAYEV STREET 22A",
          "city": "ALMATY",
          "stateProvince": "ALMATY REGION",
          "postalZoneZipCode": "050013",
          "countryRegion": "KAZAKHSTAN"  
        },
        "telephoneNumber": "******-111-2222",
        "jobTitle": "JUNIOR DEVELOPER",
        "supervisorSurnames": "PETROV", 
        "supervisorGivenNames": "IVAN SERGEEVICH",
        "employmentDateFrom": "2018-08-01", 
        "employmentDateTo": "2020-05-31",
        "duties": "WEB DEVELOPMENT AND TECHNICAL SUPPORT"
      }
    ],
    "previousEducation": [
      {
        "institutionName": "KAZAKH NATIONAL UNIVERSITY",
        "address": {
          "streetLine1": "AL-FARABI AVENUE 71",
          "city": "ALMATY",
          "stateProvince": "ALMATY REGION",
          "postalZoneZipCode": "050040", 
          "countryRegion": "KAZAKHSTAN"
        },
        "courseOfStudy": "COMPUTER SCIENCE",
        "dateOfAttendanceFrom": "2014-09-01",
        "dateOfAttendanceTo": "2018-06-30"
      },
      {
        "institutionName": "ALMATY MANAGEMENT UNIVERSITY",
        "address": {
          "streetLine1": "ROZYBAKIEV STREET 227",
          "city": "ALMATY",
          "stateProvince": "ALMATY REGION", 
          "postalZoneZipCode": "050060",
          "countryRegion": "KAZAKHSTAN"
        },
        "courseOfStudy": "PROJECT MANAGEMENT CERTIFICATION",
        "dateOfAttendanceFrom": "2019-03-01", 
        "dateOfAttendanceTo": "2019-12-31"
      }
    ]
  }
}
```

### 3. Military Service with Full Details
```json
{
  "workAndEducation": {
    "additionalInfo": {
      "hasServedInMilitary": {
        "answer": "Yes",
        "country": "KAZAKHSTAN",
        "branchOfService": "ARMY",
        "rankPosition": "SERGEANT", 
        "militarySpecialty": "COMMUNICATIONS SPECIALIST",
        "dateOfServiceFrom": "2012-10-01",
        "dateOfServiceTo": "2013-09-30"
      }
    }
  }
}
```

### 4. Self-Employed/Business Owner
```json
{
  "workAndEducation": {
    "present": {
      "primaryOccupation": "ARCHITECTURE",
      "employerOrSchoolName": "SELF-EMPLOYED",
      "address": {
        "streetLine1": "HOME OFFICE - MANGILIK EL 55",
        "city": "ASTANA",
        "stateProvince": "AKMOLA REGION",
        "postalZoneZipCode": "010000",
        "countryRegion": "KAZAKHSTAN"
      },
      "startDate": "2020-01-01",
      "monthlyIncomeLocal": "600000", 
      "duties": "ARCHITECTURAL DESIGN AND CONSULTATION SERVICES"
    }
  }
}
```

### 5. Unemployed/Between Jobs
```json
{
  "workAndEducation": {
    "present": {
      "primaryOccupation": "UNEMPLOYED",
      "employerOrSchoolName": "UNEMPLOYED",
      "address": {
        "streetLine1": "HOME ADDRESS",
        "city": "ALMATY",
        "stateProvince": "ALMATY REGION",
        "postalZoneZipCode": "050000",
        "countryRegion": "KAZAKHSTAN"
      },
      "startDate": "2023-06-01",
      "monthlyIncomeLocal": "0",
      "duties": "SEEKING EMPLOYMENT"
    }
  }
}
```

### 6. Full Additional Info Example
```json
{
  "workAndEducation": {
    "additionalInfo": {
      "clanOrTribeName": "NAIMAN",
      "languagesSpoken": ["KAZAKH", "RUSSIAN", "ENGLISH", "TURKISH"],
      "countriesVisitedLastFiveYears": [
        "TURKEY", "UNITED ARAB EMIRATES", "RUSSIA", "KYRGYZSTAN", "UZBEKISTAN"
      ],
      "charitableOrganizationsWorkedFor": {
        "answer": "Yes",
        "organizationName": "KAZAKH RED CRESCENT SOCIETY - VOLUNTEER WORK"
      },
      "specializedSkills": {
        "answer": "Yes",
        "explanation": "ADVANCED CLOUD COMPUTING, BLOCKCHAIN DEVELOPMENT, CYBERSECURITY EXPERTISE"
      },
      "hasServedInMilitary": {
        "answer": "Yes", 
        "country": "KAZAKHSTAN",
        "branchOfService": "ARMY",
        "rankPosition": "SERGEANT",
        "militarySpecialty": "COMMUNICATIONS AND IT SUPPORT",
        "dateOfServiceFrom": "2012-10-01",
        "dateOfServiceTo": "2013-09-30"
      },
      "hasBeenInRebelGroup": {
        "answer": "No",
        "explanation": ""
      }
    }
  }
}
```

---

## Contact Info Examples

### 1. Basic Contact (Single Address/Phone/Email)
```json
{
  "contactInfo": {
    "homeAddress": {
      "streetLine1": "AL-FARABI AVENUE 123",
      "city": "ALMATY",
      "stateProvince": "ALMATY REGION", 
      "postalZoneZipCode": "050040",
      "countryRegion": "KAZAKHSTAN"
    },
    "phoneNumbers": {
      "primary": "******-123-4567"
    },
    "emailAddresses": {
      "primary": "<EMAIL>"
    },
    "socialMedia": []
  }
}
```

### 2. Full Contact with All Fields
```json
{
  "contactInfo": {
    "homeAddress": {
      "streetLine1": "AL-FARABI AVENUE 123",
      "streetLine2": "APARTMENT 45",
      "city": "ALMATY",
      "stateProvince": "ALMATY REGION",
      "postalZoneZipCode": "050040", 
      "countryRegion": "KAZAKHSTAN"
    },
    "mailingAddress": {
      "streetLine1": "P.O. BOX 1234",
      "city": "ALMATY",
      "stateProvince": "ALMATY REGION",
      "postalZoneZipCode": "050000",
      "countryRegion": "KAZAKHSTAN"
    },
    "phoneNumbers": {
      "primary": "******-123-4567",
      "secondary": "******-987-6543",
      "work": "******-244-5555"
    },
    "emailAddresses": {
      "primary": "<EMAIL>",
      "additional": "<EMAIL>"
    },
    "socialMedia": [
      {
        "platform": "FACEBOOK", 
        "identifier": "ivan.ivanov.kz"
      },
      {
        "platform": "INSTAGRAM",
        "identifier": "@ivan_ivanov_kz" 
      },
      {
        "platform": "TWITTER",
        "identifier": "@ivanivanov"
      }
    ]
  }
}
```

---

## Travel Info Examples

### 1. First-Time Business Visitor
```json
{
  "travelInfo": {
    "purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)",
    "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)",
    "hasSpecificTravelPlans": "Yes",
    "arrivalDate": "2025-03-15",
    "arrivalFlight": "KC888",
    "arrivalCity": "NEW YORK",
    "departureDate": "2025-03-25",
    "departureFlight": "KC889", 
    "departureCity": "NEW YORK",
    "locationsToVisitInUS": ["NEW YORK", "WASHINGTON DC", "CHICAGO"],
    "usStayAddress": {
      "streetLine1": "123 BROADWAY",
      "city": "NEW YORK",
      "state": "NEW YORK",
      "zipCode": "10001"
    },
    "personEntityPaying": "EMPLOYER"
  }
}
```

### 2. Tourism with Family
```json
{
  "travelInfo": {
    "purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)",
    "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)", 
    "hasSpecificTravelPlans": "Yes",
    "arrivalDate": "2025-07-01",
    "arrivalCity": "LOS ANGELES",
    "departureDate": "2025-07-21",
    "departureCity": "LOS ANGELES",
    "locationsToVisitInUS": [
      "LOS ANGELES", "SAN FRANCISCO", "LAS VEGAS", "GRAND CANYON"
    ],
    "usStayAddress": {
      "streetLine1": "456 HOTEL AVENUE", 
      "city": "LOS ANGELES",
      "state": "CALIFORNIA",
      "zipCode": "90210"
    },
    "personEntityPaying": "SELF"
  }
}
```

---

## Travel Companions Examples

### 1. No Companions (Solo Travel)
```json
{
  "travelCompanions": {
    "areOtherPersonsTraveling": "No",
    "personsTraveling": [],
    "isTravelingAsPartOfGroup": "No"
  }
}
```

### 2. Family Travel with Children
```json
{
  "travelCompanions": {
    "areOtherPersonsTraveling": "Yes",
    "personsTraveling": [
      {
        "surnames": "IVANOVA",
        "givenNames": "ELENA PETROVNA",
        "relationship": "SPOUSE"
      },
      {
        "surnames": "IVANOV", 
        "givenNames": "ALEXANDER IVANOVICH",
        "relationship": "CHILD"
      },
      {
        "surnames": "IVANOVA",
        "givenNames": "MARIA IVANOVNA", 
        "relationship": "CHILD"
      }
    ],
    "isTravelingAsPartOfGroup": "No"
  }
}
```

### 3. Business Group Travel
```json
{
  "travelCompanions": {
    "areOtherPersonsTraveling": "Yes", 
    "personsTraveling": [
      {
        "surnames": "PETROV",
        "givenNames": "SERGEI ALEXANDROVICH",
        "relationship": "OTHER"
      },
      {
        "surnames": "SIDOROV", 
        "givenNames": "DMITRY NIKOLAEVICH",
        "relationship": "OTHER"
      }
    ],
    "isTravelingAsPartOfGroup": "Yes"
  }
}
```

---

## Previous US Travel Examples

### 1. First-Time Visitor (No Previous Travel)
```json
{
  "previousUSTravel": {
    "hasEverBeenInUS": "No",
    "previousVisits": [],
    "hasEverHeldUSLicense": "No",
    "driversLicenses": [],
    "hasEverBeenIssuedUSVisa": "No", 
    "previousVisaInfo": [],
    "hasEverBeenRefusedAdmissionOrVisa": "No",
    "refusedAdmissionDetails": [],
    "hasImmigrantPetitionBeenFiled": "No",
    "immigrantPetitionDetails": []
  }
}
```

### 2. Previous US Visitor (Multiple Visits)
```json
{
  "previousUSTravel": {
    "hasEverBeenInUS": "Yes",
    "previousVisits": [
      {
        "dateArrived": "2022-06-15",
        "lengthOfStay": "14",
        "unitOfStay": "DAY(S)"
      },
      {
        "dateArrived": "2020-09-10", 
        "lengthOfStay": "21",
        "unitOfStay": "DAY(S)"
      },
      {
        "dateArrived": "2018-12-01",
        "lengthOfStay": "1",
        "unitOfStay": "MONTH(S)"
      }
    ],
    "hasEverHeldUSLicense": "Yes",
    "driversLicenses": [
      {
        "licenseNumber": "DL*********", 
        "issuingStateProvince": "CALIFORNIA"
      }
    ],
    "hasEverBeenIssuedUSVisa": "Yes",
    "previousVisaInfo": [
      {
        "dateLastVisaIssued": "2022-05-01",
        "annotation": "B1/B2",
        "isApplyingForSameType": "Yes",
        "isApplyingInSameCountry": "Yes",
        "hasBeenTenPrinted": "Yes"
      },
      {
        "dateLastVisaIssued": "2018-04-15",
        "annotation": "B1/B2", 
        "isApplyingForSameType": "Yes",
        "isApplyingInSameCountry": "Yes",
        "hasBeenTenPrinted": "No"
      }
    ]
  }
}
```

---

## US Contact Examples

### 1. Personal Contact (Friend)
```json
{
  "usContact": {
    "contactPersonSurnames": "SMITH",
    "contactPersonGivenNames": "JOHN MICHAEL",
    "relationshipToYou": "FRIEND",
    "address": {
      "streetLine1": "456 FIFTH AVENUE",
      "city": "NEW YORK", 
      "state": "NEW YORK",
      "zipCode": "10001"
    },
    "phone": "******-123-4567",
    "email": "<EMAIL>"
  }
}
```

### 2. Business/Organization Contact
```json
{
  "usContact": {
    "contactPersonSurnames": "JOHNSON",
    "contactPersonGivenNames": "SARAH ELIZABETH",
    "organizationName": "TECH INNOVATIONS INC",
    "relationshipToYou": "BUSINESS ASSOCIATE",
    "address": {
      "streetLine1": "789 TECHNOLOGY DRIVE", 
      "city": "SAN FRANCISCO",
      "state": "CALIFORNIA", 
      "zipCode": "94105"
    },
    "phone": "******-555-9876",
    "email": "<EMAIL>"
  }
}
```

---

## Family Info Examples

### 1. Both Parents Living (Not in US)
```json
{
  "familyInfo": {
    "father": {
      "surnames": "IVANOV",
      "givenNames": "SERGEI PETROVICH",
      "dateOfBirth": "1960-03-20", 
      "isInTheUS": "No",
      "status": "FOREIGN NATIONAL"
    },
    "mother": {
      "surnames": "IVANOVA", 
      "givenNames": "MARIA ALEXANDROVNA",
      "dateOfBirth": "1965-08-10",
      "isInTheUS": "No",
      "status": "FOREIGN NATIONAL"
    },
    "hasImmediateRelativesInUS": "No",
    "immediateRelatives": []
  }
}
```

### 2. Complex Family (Parents in US, Siblings)
```json
{
  "familyInfo": {
    "father": {
      "surnames": "PETROV",
      "givenNames": "ALEXANDER DMITRIEVICH",
      "dateOfBirth": "1958-12-05",
      "isInTheUS": "Yes", 
      "status": "US CITIZEN"
    },
    "mother": {
      "surnames": "PETROVA",
      "givenNames": "ELENA SERGEEVNA", 
      "dateOfBirth": "1962-07-18",
      "isInTheUS": "Yes",
      "status": "LEGAL PERMANENT RESIDENT"
    },
    "hasImmediateRelativesInUS": "Yes",
    "immediateRelatives": [
      {
        "surnames": "PETROV",
        "givenNames": "MIKHAIL ALEXANDROVICH",
        "relationship": "SIBLING",
        "status": "US CITIZEN"
      },
      {
        "surnames": "WILLIAMS",
        "givenNames": "ANNA PETROVNA", 
        "relationship": "SIBLING",
        "status": "LEGAL PERMANENT RESIDENT"
      }
    ]
  }
}
```

---

## Security and Background Examples

### 1. Clean Record (All No)
```json
{
  "securityAndBackground": {
    "part1MedicalAndHealth": {
      "hasCommunicableDisease": {"answer": "No"},
      "hasMentalOrPhysicalDisorder": {"answer": "No"},
      "isDrugAbuserOrAddict": {"answer": "No"}
    },
    "part2Criminal": {
      "hasBeenArrested": {"answer": "No"},
      "hasViolatedControlledSubstanceLaws": {"answer": "No"}, 
      "engagedInProstitution": {"answer": "No"},
      "engagedInMoneyLaundering": {"answer": "No"},
      "committedHumanTrafficking": {"answer": "No"},
      "aidedHumanTrafficking": {"answer": "No"},
      "isFamilyOfHumanTrafficker": {"answer": "No"}
    },
    "part3Security": {
      "engagedInEspionageOrSabotage": {"answer": "No"},
      "engagedInTerroristActivities": {"answer": "No"},
      "providedSupportToTerrorists": {"answer": "No"},
      "isMemberOfTerroristOrganization": {"answer": "No"},
      "participatedInGenocide": {"answer": "No"},
      "participatedInTorture": {"answer": "No"}, 
      "participatedInExtrajudicialKillings": {"answer": "No"},
      "violatedReligiousFreedoms": {"answer": "No"},
      "involvedInForcedPopulationControl": {"answer": "No"},
      "involvedInCoerciveOrganTransplantation": {"answer": "No"}
    },
    "part4ImmigrationViolations": {
      "soughtVisaByFraud": {"answer": "No"},
      "failedToAttendRemovalHearings": {"answer": "No"},
      "beenUnlawfullyPresentOrOverstayed": {"answer": "No"},
      "beenRemovedOrDeported": {"answer": "No"}
    },
    "part5Miscellaneous": {
      "withheldCustodyOfUSCitizenChild": {"answer": "No"},
      "votedInUSIllegally": {"answer": "No"},
      "renouncedUSCitizenshipForTax": {"answer": "No"},
      "attendedPublicSchoolOnStudentVisaWithoutReimbursement": {"answer": "No"}
    }
  }
}
```

### 2. Mixed Answers with Explanations
```json
{
  "securityAndBackground": {
    "part1MedicalAndHealth": {
      "hasCommunicableDisease": {"answer": "No"},
      "hasMentalOrPhysicalDisorder": {
        "answer": "Yes",
        "explanation": "PREVIOUSLY TREATED DEPRESSION IN 2018 - FULLY RECOVERED WITH MEDICAL CLEARANCE"
      },
      "isDrugAbuserOrAddict": {"answer": "No"}
    },
    "part2Criminal": {
      "hasBeenArrested": {
        "answer": "Yes", 
        "explanation": "MINOR TRAFFIC VIOLATION IN 2015 - PAID FINE, NO CRIMINAL RECORD"
      },
      "hasViolatedControlledSubstanceLaws": {"answer": "No"},
      "engagedInProstitution": {"answer": "No"},
      "engagedInMoneyLaundering": {"answer": "No"},
      "committedHumanTrafficking": {"answer": "No"},
      "aidedHumanTrafficking": {"answer": "No"},
      "isFamilyOfHumanTrafficker": {"answer": "No"}
    }
  }
}
```

---

## Special Scenarios

### 1. CIS Country Variations

#### Kazakhstan Example
```json
{
  "personalInfo": {
    "surnames": "NAZARBAYEV",
    "givenNames": "AIDA NURLANOVNA",
    "fullNameNativeAlphabet": "НАЗАРБАЕВ АИДА НУРЛАНОВНА"
  },
  "nationalityAndResidence": {
    "countryOfOrigin": "KAZAKHSTAN",
    "nationalIdentificationNumber": "920515400123"
  },
  "passportInfo": {
    "passportType": "REGULAR", 
    "passportNumber": "N12345678",
    "issuingCountry": "KAZAKHSTAN",
    "issuingCity": "ALMATY"
  }
}
```

#### Russia Example  
```json
{
  "personalInfo": {
    "surnames": "PETROV",
    "givenNames": "DMITRY ALEXANDROVICH", 
    "fullNameNativeAlphabet": "ПЕТРОВ ДМИТРИЙ АЛЕКСАНДРОВИЧ"
  },
  "nationalityAndResidence": {
    "countryOfOrigin": "RUSSIA",
    "nationalIdentificationNumber": "*********0"
  },
  "passportInfo": {
    "passportType": "REGULAR",
    "passportNumber": "*********",
    "issuingCountry": "RUSSIA",
    "issuingCity": "MOSCOW"
  }
}
```

### 2. Student Visa Scenarios

#### F-1 Student Application
```json
{
  "travelInfo": {
    "purposeOfTrip": "STUDENT - ACADEMIC (F)",
    "visaClass": "STUDENT - ACADEMIC (F1)",
    "hasSpecificTravelPlans": "Yes",
    "arrivalDate": "2025-08-15",
    "locationsToVisitInUS": ["BOSTON"],
    "personEntityPaying": "PARENTS"
  },
  "workAndEducation": {
    "present": {
      "primaryOccupation": "STUDENT",
      "employerOrSchoolName": "MASSACHUSETTS INSTITUTE OF TECHNOLOGY",
      "address": {
        "streetLine1": "77 MASSACHUSETTS AVENUE", 
        "city": "CAMBRIDGE",
        "stateProvince": "MASSACHUSETTS", 
        "postalZoneZipCode": "02139",
        "countryRegion": "UNITED STATES"
      },
      "startDate": "2025-09-01",
      "duties": "COMPUTER SCIENCE GRADUATE STUDIES"
    }
  }
}
```

### 3. Business/Employment Scenarios

#### H-1B Professional Worker
```json
{
  "travelInfo": {
    "purposeOfTrip": "TEMP WORKER - SPECIALTY OCCUPATION (H)", 
    "visaClass": "TEMPORARY WORKER - SPECIALTY OCCUPATION (H1B)",
    "personEntityPaying": "EMPLOYER"
  },
  "usContact": {
    "contactPersonSurnames": "ANDERSON",
    "contactPersonGivenNames": "ROBERT JAMES",
    "organizationName": "GOOGLE INC",
    "relationshipToYou": "PROSPECTIVE EMPLOYER",
    "address": {
      "streetLine1": "1600 AMPHITHEATRE PARKWAY",
      "city": "MOUNTAIN VIEW",
      "state": "CALIFORNIA", 
      "zipCode": "94043"
    }
  }
}
```

---

## Complete Example Files Reference

This document serves as a comprehensive reference. For complete working examples, see:

- `sample_application.json` - Complete B1/B2 tourism/business example
- `test_case_married_with_spouse.json` - Married applicant scenario
- `test_case_divorced_with_former_spouse.json` - Divorced applicant scenario  
- `test_widowed_with_deceased_spouse.json` - Widowed applicant scenario
- `test_case_minor_under_14.json` - Minor child application
- `cis_*.json` files - CIS country specific examples

## Notes

1. **Date Format**: Always use ISO format `YYYY-MM-DD`
2. **Address Format**: Follow DS-160 field requirements exactly
3. **Phone Numbers**: Include country code (e.g., `******-123-4567`)
4. **Names**: Use ALL CAPS as required by DS-160 forms
5. **Countries**: Use official DS-160 country names (check dropdown values)
6. **Field Validation**: All examples follow Pydantic model validation rules

## ⚠️ Important Validation Rules

### Street Address Validation
**DS-160 has strict character validation for all street address fields.**

**✅ Allowed Characters:**
- Letters: A-Z (uppercase only)
- Numbers: 0-9
- Special characters: `# $ * % & (; ) ! @ ^ ? > < ( ) . ' , - ` and space

**❌ Common Invalid Characters:**
- Forward slash: `/` ❌ 
- Square brackets: `[ ]` ❌
- Curly braces: `{ }` ❌
- Backslash: `\` ❌
- Pipe: `|` ❌
- Plus: `+` ❌
- Equals: `=` ❌
- Underscores: `_` ❌

**Examples:**
```json
// ✅ Valid addresses
"streetLine1": "AL-FARABI AVENUE 77-8"     // Hyphen allowed
"streetLine1": "123 MAIN ST., APT #5"      // Period, comma, hash allowed
"streetLine1": "P.O. BOX 1234"             // Periods allowed
"streetLine1": "BUILDING A (SUITE 100)"    // Parentheses allowed

// ❌ Invalid addresses  
"streetLine1": "AL-FARABI AVENUE 77/8"     // Forward slash not allowed
"streetLine1": "MAIN ST [BUILDING A]"      // Square brackets not allowed
"streetLine1": "OFFICE_COMPLEX"            // Underscore not allowed
"streetLine1": "ADDRESS+DETAILS"           // Plus sign not allowed
```

**Validation is applied to:**
- All `streetLine1` and `streetLine2` fields
- Home addresses, mailing addresses, work addresses
- US contact addresses, previous work addresses
- All address types in the application

---

*Last Updated: 2025-01-07*